{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:unit": "vitest run tests/unit", "test:unit:watch": "vitest tests/unit", "test:integration": "vitest run tests/integration", "test:integration:watch": "vitest tests/integration", "test:coverage": "vitest run --coverage", "test:all": "npm run test:unit && npm run test:integration", "prepare": "cypress install", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "test:e2e:headless": "start-server-and-test preview http://localhost:4173 'cypress run --e2e --headless'", "test:component": "cypress run --component", "test:component:dev": "cypress open --component", "test:full": "npm run test:all && npm run test:e2e:headless", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@vue-leaflet/vue-leaflet": "^0.10.1", "leaflet": "^1.9.4", "pinia": "^3.0.3", "three": "^0.178.0", "vue": "^3.5.17", "vue-datepicker-next": "^1.0.3", "vue-router": "^4.5.1", "@vitest/ui": "latest", "vitest": "latest"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/test-utils": "^2.4.6", "cypress": "^14.5.0", "eslint": "^9.29.0", "eslint-plugin-cypress": "^5.1.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "jsdom": "^26.1.0", "prettier": "3.5.3", "start-server-and-test": "^2.0.12", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4"}}