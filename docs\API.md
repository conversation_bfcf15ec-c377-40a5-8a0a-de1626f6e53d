RESTful API
Properties API
1. GET /api/v1/properties
Request:
http
GET /api/v1/properties?type=APARTMENT&dealType=SALE&minPrice=1000000&maxPrice=5000000&minRooms=2&maxRooms=3&regionuuid=5&agentuuid=42&features=PARKING,BALCONY&includeNoCoordinates=true&page=0&size=10
Response (200 OK):
json
{
  "content": [
    {
      "propertyUuid": “”, +
      "userUuid": , +
      "regionId": , - стереть
      "title": "", +
      "type": "", +
      "dealType": "", +
      "price": 4500000, +
      "rooms": 2, +
      "area": 65.4, +
      "features": ["PARKING", "BALCONY"], + 
      "address": "", +
      "latitude": 55.751244, -
      "longitude": 37.618423, -
      "createdAt": "2023-10-05T12:30:45Z" +,
      "updatedAt": "2023-10-05T12:30:45Z" -
    }
  ],
  "pageable": {
    "pageNumber": 0,
    "pageSize": 10,
    "sort": { "sorted": false }
  },
  "totalPages": 1,
  "totalElements": 1,
  "last": true
}

2. GET /api/v1/properties/{uuid}
Request:
http
GET /api/v1/properties/geerg-gergrg-rgergerg
Response (200 OK):
json
{
  "propertyUuid":”geerg-gergrg-rgergerg”,
  "userUuid": ”geerg-gergrg-rgergerg”,
  "regionId": 5,
  "title": "2-комнатная квартира в центре",
  "type": "APARTMENT",
  "dealType": "SALE",
  "price": 4500000,
  "rooms": 2,
  "area": 65.4,
  "features": ["PARKING", "BALCONY"],
  "address": "ул. Ленина, 15",
  "description": "Просторная квартира с ремонтом...",
  "latitude": 55.751244,
  "longitude": 37.618423,
  "createdAt": "2023-10-05T12:30:45Z",
  "updatedAt": "2023-10-05T12:30:45Z"
}

3. POST /api/v1/properties
Request:
http
POST /api/v1/properties
Authorization: Bearer <access_token>
json
{
  "userUuid": ”geerg-gergrg-rgergerg”,
  "regionId": 5,
  "title": "2-комнатная квартира в центре",
  "type": "APARTMENT",
  "dealType": "SALE",
  "price": 4500000,
  "rooms": 2,
  "area": 65.4,
  "features": ["PARKING", "BALCONY"],
  "address": "ул. Ленина, 15", 
  "description": "Просторная квартира с ремонтом...", 
  "latitude": 55.751244,
  "longitude": 37.618423
}
Response (201 Created):
json
{
  "propertyUuid": “”, 
  "userUuid": , 
  "regionId": , 
   "title": "", 
   "type": "", 
   "dealType": "", 
   "price": 4500000, 
   "rooms": 2,
   "area": 65.4, 
   "features": ["PARKING", "BALCONY"], 
   "address": "", 
   "latitude": 55.751244, 
   "longitude": 37.618423, 
   "createdAt": "2023-10-05T12:30:45Z",
   "updatedAt": "2023-10-05T12:30:45Z" 
}

4. PUT /api/v1/properties/{uuid}
Request:
http
PUT /api/v1/properties/geerg-gergrg-rgergerg
Authorization: Bearer <access_token>
json
{
  "price": 37000,
  "features": ["ELEVATOR", "BALCONY"]
}
Response (200 OK):
json
{
  "propertyUuid": “”, 
  "userUuid": , 
  "regionId": , 
  "title": "", 
  "type": "", 
  "dealType": "", 
  "price": 37000, 
  "rooms": 2,
  "area": 65.4, 
  "features": ["ELEVATOR", "BALCONY"], 
  "address": "", 
  "latitude": 55.751244, 
  "longitude": 37.618423, 
  "createdAt": "2023-10-05T12:30:45Z",
  "updatedAt": "2023-10-05T12:30:45Z" 
}

5. DELETE /api/v1/properties/{uuid}
Request:
http
DELETE /api/v1/properties/geerg-gergrg-rgergerg
Authorization: Bearer <access_token>
Response (200 OK):
Empty body

User API
1. GET /api/v1/users
Request:
http
GET /api/v1/users?page=0&size=5
Response (200 OK):
json
{
  "content": [
    {
      "userUuid": “geerg-gergrg-rgergerg”,
"role": “AGENT”,
"regionId": 1,
      "firstName": "Иван",
      "lastName": "Иванов",
      "email": "<EMAIL>",
      "phone": "+79161234567",
      "createdAt": "2023-09-01T10:00:00Z",
"updatedAt": "2023-09-01T10:00:00Z",
	“isBlocked": false
    }
  ],
  "pageable": {
    "pageNumber": 0,
    "pageSize": 5,
    "sort": { "sorted": false }
  },
  "totalElements": 1
}

2. GET /api/v1/agents/{useruUuid}
Request:
http
GET /api/v1/agents/geerg-gergrg-rgergerg
Response (200 OK):
json
{
  "userUuid": “geerg-gergrg-rgergerg”,
  "role": “AGENT”,
  "regionId": 1,
  "firstName": "Иван",
  "lastName": "Иванов",
  "email": "<EMAIL>",
  "phone": "+79161234567",
  "createdAt": "2023-09-01T10:00:00Z",
  "updatedAt": "2023-09-01T10:00:00Z",
  “isBlocked": false
}

3. GET /api/v1/agents/{useruUuid}/properties
Request:
http
GET /api/v1/agents/geerg-gergrg-rgergerg/properties
Response (200 OK):
json
[
  {
      "propertyUuid": “geerg-gergrg-rgergerg”, +
      "title": "", +
      "type": "", +
      "dealType": "", +
      "price": 4500000, +
      "rooms": 2, +
      "area": 65.4, +
      "address": "", +
      "createdAt": "2023-10-05T12:30:45Z" +,
      "updatedAt": "2023-10-05T12:30:45Z" +


  }
]
5. PUT /api/v1/agents/{uuid}
Request:
http
PUT /api/v1/agents/geerg-gergrg-rgergerg
Authorization: Bearer <admin_token>
json
{
  "phone": "+79167778899"
}
Response (200 OK):
json
{
  "userUuid": “geerg-gergrg-rgergerg”,
  "role": “AGENT”,
  "regionId": 1,
  "firstName": "Иван",
  "lastName": "Иванов",
  "email": "<EMAIL>",
  "phone": "+79167778899",
  "createdAt": "2023-09-01T10:00:00Z",
  "updatedAt": "2023-09-01T10:00:00Z",
  “isBlocked": false
}

6. DELETE /api/v1/agents/{uuid}
Request:
http
DELETE /api/v1/agents/geerg-gergrg-rgergerg
Authorization: Bearer <admin_token>
Response (204 No Content):
Empty body

7. GET /api/v1/agents/{uuid}/stats 
Request:
http
GET /api/v1/agents/geerg-gergrg-rgergerg/stats
Authorization: Bearer <admin_token>
Response (200 OK):
json
{
  "propertyCount": 5,
  "viewCount": 150,
  "lastUpdated": "2025-07-01T08:50:00Z"
}

8. GET /api/v1/agents/{uuid}/export
Request:
http
GET /api/v1/agents/53/stats
Authorization: Bearer <admin_token>
Response (200 OK):
Content-Type: text/csv
Тело: uuid,title,price\n1,Квартира,4500000

9. PUT /api/v1/agents/{uuid}/block
Request:
http
GET /api/v1/agents/geerg-gergrg-rgergerg/stats
Authorization: Bearer <admin_token>
Response (200 OK):
json
{
  "userUuid": “geerg-gergrg-rgergerg”,
  "role": “AGENT”,
  "regionId": 1,
  "firstName": "Иван",
  "lastName": "Иванов",
  "email": "<EMAIL>",
  "phone": "+79167778899",
  "createdAt": "2023-09-01T10:00:00Z",
  "updatedAt": "2023-09-01T10:00:00Z",
  “isBlocked": true

}
10. PUT /api/v1/agents/{uuid}/unblock
Request:
http
GET /api/v1/agents/geerg-gergrg-rgergerg/stats
Authorization: Bearer <admin_token>
Response (200 OK):
json
{
  "userUuid": “geerg-gergrg-rgergerg”,
  "role": “AGENT”,
  "regionId": 1,
  "firstName": "Иван",
  "lastName": "Иванов",
  "email": "<EMAIL>",
  "phone": "+79167778899",
  "createdAt": "2023-09-01T10:00:00Z",
  "updatedAt": "2023-09-01T10:00:00Z",
  “isBlocked": false

}


Authentication API
1. POST /api/v1/auth/login
Request:
http
Д
json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
Response (200 OK):
json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600,
  "role": "AGENT"
}

2. POST /api/v1/auth/register
Request:
http
POST /api/v1/auth/register
json
{
  "firstName": "Алексей",
  "lastName": "Смирнов",
  "email": "<EMAIL>”,
  "password": "1234567890",
  "password_confirmation": "1234567890",
  "phone": "+1234567890"
}
Response (201 Created):
json
{
  "uuid": 100,
  "firstName": "Алексей",
  "lastName": "Смирнов",
  "email": "<EMAIL>",
  "role": "USER",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

3. POST /api/v1/auth/refresh
Request:
http
POST /api/v1/auth/refresh
json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
Response (200 OK):
json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600
}

Особенности реализации:
Авторизация:
Защищенные методы требуют Authorization: Bearer <token>
Роли: ADMIN, REGIONAL_ADMIN, AGENT, GUEST
Ошибки:
401 Unauthorized: Неверные учетные данные
403 Forbidden: Нет прав доступа
404 Not Found: Ресурс не найден
400 Bad Request: Ошибки валидации
409 Conflict: Ошибка при попытке сделать запись.
500 Internal Server Error: Внутренняя ошибка сервера
502 Bad Gateway: неверный адрес
Валидация:
Bean Validation (JSR-380) для всех DTO
Глобальный обработчик исключений
Пагинация:
Стандарт Spring Pageable (page, size)
Ответ содержит метаданные пагинации
Даты:
Формат ISO-8601 (yyyy-MM-dd'T'HH:mm:ss'Z')
UTC временная зона
Компетенции и доступ к эндпоинтам
Роли и их компетенции
 ADMIN: Полный доступ к системе (управление всеми агентами, свойствами, авторизация). 
REGIONAL_ADMIN: Управление агентами и недвижимостью только в своем регионе (проверяется по regionUuid).
 AGENT: Управление своими свойствами и просмотр своей статистики/портфеля.
GUEST (без токена): Доступ только к публичным данным (GET /users, /users/{code}/properties).
Доступ к эндпоинтам
Эндпоинт
ADMIN
REGIONAL_ADMIN
AGENT
Guest
Properties API (Internal)
Prefix: /api/v1/
 
 
 
 
GET /properties
Да
Да
Нет
Нет
GET /properties/{uuid}
Да
Да
Нет
Нет
POST /properties
Да
Да (свой регион)
Нет
Нет
PUT /properties/{uuid}
Да
Да (свой регион)
Нет
Нет
DELETE /properties/{uuid}
Да
Да (свой регион)
Нет
Нет
Guest API (External)
Prefix: /api/v1/








GET /agents/{agentUuid}/properties
Нет
Нет
Нет
Да
POST /api/v1/agent-requests


Нет
Нет
Нет
Да
Agents API (External)
Prefix: /api/v1/
 
 
 
 
GET /agents
Да
Да
Нет
Нет
GET /agents/{agentUuid} +
Да
Да
Да
Да
PUT /agents/{agentUuid} +
Да
Да (свой регион)
Да (свой)
Нет
DELETE /agents/{agentUuid}
Да
Да (свой регион)
Да (свой)
Нет
GET /agents/{agentUuid}/stats
Да
Да (свой регион)
Да (свой)
Нет
GET /agents/{agentUuid}/export
Да
Да (свой регион)
Да (свой)
Нет
PUT /agents/{agentUuid}/block
Да
Да (свой регион)
Нет
Нет
PUT /agents/{agentUuid}/unblock
Нет
Да (свой регион)
Нет
Нет
GET /agents/{agentUuid}/properties +
Да
Да
Да
Да
POST /agents/{agentUuid}/properties +
Да
Да (свой регион)
Да
Нет
GET /agents/{agentUuid}/properties/{uuid} +
Да
Да
Да
Да
PUT /agents/{agentUuid}/properties/{uuid}
Да
Да (свой регион)
Да (свой)
Нет
DELETE /agents/{agentUuid}/properties/{uuid} +
Да
Да (свой регион)
Да (свой)
Нет
GET /agents/{agentUuid}/properties/{uuid}/calendar +








PUT /agents/{agentUuid}/properties/{uuid}/calendar +








Regional Administrators API (External)
Prefix: /api/v1/








GET /api/v1/agent-requests 
Получение списка заявок на регистрацию риэлтора








POST /api/v1/agent-requests/{id}/approve








POST /api/v1/agent-requests/{id}/reject








Global Administrator API (External)
Prefix: /api/v1/








GET /api/v1/admin-requests (Ticket)
Получение списка заявок на регистрацию регионального администратора








POST /api/v1/admin-requests/{id}/approve (Ticket)








POST /api/v1/admin-requests/{id}/reject
(Ticket)








GET /api/v1/admins
Получение списка всех  региональных администраторов








PUT /admins/{adminUuid}/block








PUT /admins/{adminUuid}/unblock








Authorization API (External)
Prefix: /api/v1/








POST /auth/register (Ticket)
Нет
Нет
Нет
Да
POST /auth/login (Auth)
Нет
Нет
Нет
Да
GET /auth/token
Да
Да
Да
Нет
Other API (External)
Prefix: /api/v1/








GET /regions
Да
Да
Да
Да
GET /api/v1/questions
Получаем список вопросов адресованных конкретному региональному администратору или глобальному администратору








POST /api/v1/questions
Задават вaпрос тут выши вон тама сверьх написанб каки то шо делаеца ↗️↗️⬆️⬆️↖️↖️








POST /api/v1/questions/{id}
Эндпоинт для ответа на конкретный вопрос










