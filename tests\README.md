# Тестирование Real Estate Management System

Этот проект включает комплексную систему тестирования с юнит-тестами, интеграционными тестами и E2E тестами.

## Структура тестов

```
tests/
├── unit/                    # Юнит-тесты
│   ├── stores/             # Тесты для Pinia stores
│   ├── services/           # Тесты для сервисов
│   └── components/         # Тесты для Vue компонентов
├── integration/            # Интеграционные тесты
└── setup.js               # Настройка тестовой среды

cypress/
├── e2e/                   # E2E тесты
├── component/             # Компонентные тесты Cypress
├── fixtures/              # Тестовые данные
└── support/               # Вспомогательные файлы
```

## Технологии

- **Vitest** - для юнит и интеграционных тестов
- **Vue Test Utils** - для тестирования Vue компонентов
- **Cypress** - для E2E и компонентных тестов
- **jsdom** - для эмуляции DOM в Node.js

## Запуск тестов

### Юнит-тесты

```bash
# Запуск всех юнит-тестов
npm run test:unit

# Запуск в watch режиме
npm run test:unit:watch

# Запуск с UI
npm run test:ui
```

### Интеграционные тесты

```bash
# Запуск интеграционных тестов
npm run test:integration

# Запуск в watch режиме
npm run test:integration:watch
```

### E2E тесты

```bash
# Запуск E2E тестов (headless)
npm run test:e2e

# Запуск E2E тестов с UI
npm run test:e2e:dev

# Запуск компонентных тестов
npm run test:component:dev
```

### Все тесты

```bash
# Запуск всех тестов
npm run test:full

# Запуск с покрытием кода
npm run test:coverage
```

## Покрытие тестами

### Юнит-тесты покрывают:

1. **Store (Pinia)**
   - Управление состоянием агента
   - Операции с недвижимостью (CRUD)
   - Управление подпиской
   - Управление модальными окнами
   - Настройки карты

2. **Services**
   - Геокодирование (прямое и обратное)
   - Локальное хранение фотографий
   - API клиент и обработка ошибок
   - Валидация данных

3. **Components**
   - Информация о подписке
   - Управление фотографиями
   - Формы недвижимости
   - Фильтры и поиск

### Интеграционные тесты покрывают:

1. **Управление недвижимостью**
   - Полный цикл создания недвижимости
   - Редактирование с сохранением фотографий
   - Удаление и фильтрация
   - Интеграция с картой

2. **Подписка**
   - Процесс оплаты
   - Управление статусом
   - Влияние на видимость недвижимости
   - История платежей

### E2E тесты покрывают:

1. **Пользовательские сценарии**
   - Создание и редактирование недвижимости
   - Загрузка фотографий
   - Геокодирование адресов
   - Оплата подписки

2. **UI/UX**
   - Отзывчивый дизайн
   - Доступность
   - Производительность
   - Обработка ошибок

## Тестовые данные

### Fixtures (cypress/fixtures/)

- `geocoding-response.json` - ответ API геокодирования
- `properties.json` - тестовые данные недвижимости
- `test-image-*.jpg` - тестовые изображения

### Моки

Все внешние зависимости мокаются:
- Leaflet карты
- API вызовы
- localStorage
- FileReader
- Геокодирование

## Лучшие практики

### Юнит-тесты

1. **Изоляция** - каждый тест независим
2. **Моки** - все внешние зависимости мокаются
3. **Покрытие** - тестируются все ветки кода
4. **Читаемость** - понятные названия и структура

### Интеграционные тесты

1. **Реальные сценарии** - тестируют взаимодействие компонентов
2. **Минимальные моки** - мокаются только внешние API
3. **Состояние** - проверяется изменение состояния приложения

### E2E тесты

1. **Пользовательские сценарии** - тестируют реальные use cases
2. **Стабильность** - используются data-testid атрибуты
3. **Производительность** - тесты оптимизированы по времени
4. **Отзывчивость** - тестируются разные размеры экрана

## Отладка тестов

### Vitest

```bash
# Запуск конкретного теста
npm run test -- --grep "Property Management"

# Запуск с отладкой
npm run test -- --inspect-brk
```

### Cypress

```bash
# Открыть Cypress Test Runner
npm run test:e2e:dev

# Запуск конкретного теста
npx cypress run --spec "cypress/e2e/propertyManagement.cy.js"
```

## CI/CD

Тесты настроены для запуска в CI/CD:

```yaml
# Пример GitHub Actions
- name: Run Unit Tests
  run: npm run test:unit

- name: Run Integration Tests
  run: npm run test:integration

- name: Run E2E Tests
  run: npm run test:e2e:headless
```

## Метрики качества

- **Покрытие кода**: >90%
- **Время выполнения**: <5 минут для всех тестов
- **Стабильность**: >95% успешных прогонов
- **Поддерживаемость**: Понятная структура и документация

## Добавление новых тестов

### Юнит-тест

1. Создать файл в соответствующей папке `tests/unit/`
2. Импортировать необходимые зависимости
3. Написать тесты с описательными названиями
4. Добавить моки для внешних зависимостей

### E2E тест

1. Создать файл в `cypress/e2e/`
2. Использовать data-testid для селекторов
3. Добавить необходимые fixtures
4. Протестировать на разных размерах экрана

## Troubleshooting

### Частые проблемы

1. **Тесты падают из-за таймаутов**
   - Увеличить timeout в конфигурации
   - Добавить ожидания для асинхронных операций

2. **Моки не работают**
   - Проверить порядок импортов
   - Убедиться, что моки очищаются между тестами

3. **E2E тесты нестабильны**
   - Использовать data-testid вместо CSS селекторов
   - Добавить явные ожидания

4. **Проблемы с картой в тестах**
   - Убедиться, что Leaflet правильно мокается
   - Добавить ожидания для загрузки карты
